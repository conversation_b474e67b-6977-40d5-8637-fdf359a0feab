-- SCORM 2004 Database Schema for MySQL

CREATE DATABASE IF NOT EXISTS scorm2004 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE scorm2004;

-- SCORM Package Table
CREATE TABLE IF NOT EXISTS scorm2004_package (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255),
    status VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    content_root VARCHAR(500)
);

-- SCORM Organization Table
CREATE TABLE IF NOT EXISTS scorm2004_org (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    package_id BIGINT NOT NULL,
    identifier VARCHAR(255),
    title VARCHAR(255),
    is_default_org BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (package_id) REFERENCES scorm2004_package(id) ON DELETE CASCADE
);

-- SCORM Resource Table
CREATE TABLE IF NOT EXISTS scorm2004_resource (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    package_id BIGINT NOT NULL,
    identifier VARCHAR(255),
    href VARCHAR(1000),
    scorm_type VARCHAR(50),
    FOREIGN KEY (package_id) REFERENCES scorm2004_package(id) ON DELETE CASCADE
);

-- SCORM Item Table
CREATE TABLE IF NOT EXISTS scorm2004_item (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    package_id BIGINT NOT NULL,
    org_id BIGINT,
    identifier VARCHAR(255),
    parent_id BIGINT,
    resource_id BIGINT,
    title VARCHAR(255),
    sequencing_json TEXT,
    is_sco BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (package_id) REFERENCES scorm2004_package(id) ON DELETE CASCADE,
    FOREIGN KEY (org_id) REFERENCES scorm2004_org(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_id) REFERENCES scorm2004_item(id) ON DELETE SET NULL,
    FOREIGN KEY (resource_id) REFERENCES scorm2004_resource(id) ON DELETE SET NULL
);

-- Indexes for better performance
CREATE INDEX idx_scorm_org_package_id ON scorm2004_org(package_id);
CREATE INDEX idx_scorm_resource_package_id ON scorm2004_resource(package_id);
CREATE INDEX idx_scorm_item_package_id ON scorm2004_item(package_id);
CREATE INDEX idx_scorm_item_org_id ON scorm2004_item(org_id);
CREATE INDEX idx_scorm_item_parent_id ON scorm2004_item(parent_id);
