# 로컬 개발환경용 설정 파일
# 사용법: mvn spring-boot:run -Dspring-boot.run.profiles=local

spring:
  datasource:
    url: *********************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.scorm.model
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

server:
  port: 8080

logging:
  level:
    com.example.scorm.mapper: DEBUG
    org.springframework.jdbc: DEBUG
