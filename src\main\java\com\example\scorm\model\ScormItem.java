
package com.example.scorm.model;

import jakarta.persistence.*;

@Entity
@Table(name = "scorm2004_item")
public class ScormItem {
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long packageId;
    private Long orgId;
    private String identifier;
    private Long parentId;
    private Long resourceId;
    private String title;
    @Lob
    private String sequencingJson;
    private boolean isSco = true;
    public Long getId() { return id; }
    public Long getPackageId() { return packageId; }
    public void setPackageId(Long packageId) { this.packageId = packageId; }
    public Long getOrgId() { return orgId; }
    public void setOrgId(Long orgId) { this.orgId = orgId; }
    public String getIdentifier() { return identifier; }
    public void setIdentifier(String identifier) { this.identifier = identifier; }
    public Long getParentId() { return parentId; }
    public void setParentId(Long parentId) { this.parentId = parentId; }
    public Long getResourceId() { return resourceId; }
    public void setResourceId(Long resourceId) { this.resourceId = resourceId; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public String getSequencingJson() { return sequencingJson; }
    public void setSequencingJson(String sequencingJson) { this.sequencingJson = sequencingJson; }
    public boolean isSco() { return isSco; }
    public void setSco(boolean sco) { isSco = sco; }
}
