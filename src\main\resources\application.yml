
spring:
  datasource:
    url: *********************************************************************************************************
    username: nidsuser
    password: @intermorph1!!
    driver-class-name: com.mysql.cj.jdbc.Driver

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.scorm.model
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
server:
  port: 8080
logging:
  level:
    org.hibernate.SQL: warn
