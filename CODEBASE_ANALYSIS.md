# SCORM 2004 Importer 코드베이스 분석

## 📋 개요

이 코드베이스는 **SCORM 2004 패키지를 임포트하는 Spring Boot 애플리케이션**입니다. ZIP 파일로 업로드된 SCORM 패키지를 안전하게 추출하고, `imsmanifest.xml`을 파싱하여 데이터베이스에 저장하는 기능을 제공합니다.

## 📁 프로젝트 구조

```
src/main/java/com/example/scorm/
├── ScormImporterApplication.java    # Spring Boot 메인 클래스
├── api/                            # REST API 컨트롤러
│   └── Scorm2004UploadController.java
├── model/                          # JPA 엔티티 모델
│   ├── ScormPackage.java           # SCORM 패키지 정보
│   ├── ScormOrg.java              # 조직 구조
│   ├── ScormItem.java             # 학습 아이템
│   └── ScormResource.java         # 리소스 정보
├── repo/                           # JPA 리포지토리
│   ├── ScormPackageRepo.java
│   ├── ScormOrgRepo.java
│   ├── ScormItemRepo.java
│   └── ScormResourceRepo.java
├── service/                        # 비즈니스 로직
│   └── Scorm2004ImportService.java # 핵심 임포트 서비스
├── parser/                         # XML 파싱
│   └── Manifest2004Parser.java    # imsmanifest.xml 파서
└── util/                          # 유틸리티
    └── SafeZip.java               # 안전한 ZIP 추출
```

## 🔧 기술 스택

- **Java 17** + **Spring Boot 3.3.2**
- **MyBatis 3.0.3** (데이터 액세스)
- **MySQL 8.0.33** (관계형 데이터베이스)
- **Jackson** (JSON 처리)
- **Maven** (빌드 도구)

## 🚀 주요 기능

### 1. API 엔드포인트

**POST /api/scorm2004/packages**
- Content-Type: `multipart/form-data`
- 파라미터: `file` (ZIP 파일)
- 기능: SCORM 패키지 업로드 및 임포트

```java
@PostMapping(value="/packages", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
public Map<String,Object> upload(@RequestPart("file") MultipartFile file) throws Exception {
    // ZIP 파일 검증 및 임포트 처리
}
```

### 2. 데이터 모델

#### ScormPackage (scorm2004_package)
- `id`: 패키지 고유 ID
- `title`: 패키지 제목
- `status`: 상태 (ACTIVE 등)
- `createdAt`: 생성 시간
- `contentRoot`: 추출된 파일 경로

#### ScormOrg (scorm2004_org)
- `packageId`: 패키지 ID (FK)
- `identifier`: 조직 식별자
- `title`: 조직 제목
- `isDefaultOrg`: 기본 조직 여부

#### ScormItem (scorm2004_item)
- `packageId`: 패키지 ID (FK)
- `orgId`: 조직 ID (FK)
- `identifier`: 아이템 식별자
- `parentId`: 부모 아이템 ID
- `resourceId`: 리소스 ID (FK)
- `title`: 아이템 제목
- `sequencingJson`: 시퀀싱 규칙 (JSON)
- `isSco`: SCO 여부

#### ScormResource (scorm2004_resource)
- `packageId`: 패키지 ID (FK)
- `identifier`: 리소스 식별자
- `href`: 리소스 파일 경로
- `scormType`: SCORM 타입 (sco/asset)

### 3. 핵심 임포트 프로세스

```java
public Map<String, Object> importZip(MultipartFile zip) throws Exception {
    // 1. 안전한 ZIP 추출
    Path dest = Files.createTempDirectory("scorm2004-");
    SafeZip.extractZip(zip.getInputStream(), dest);

    // 2. imsmanifest.xml 파일 찾기
    Path manifest = Files.walk(dest)
            .filter(p -> p.getFileName().toString().equalsIgnoreCase("imsmanifest.xml"))
            .findFirst().orElseThrow();

    // 3. 패키지 정보 저장
    ScormPackage sp = new ScormPackage();
    sp = pkgRepo.save(sp);

    // 4. XML 파싱
    Manifest2004Parser parser = new Manifest2004Parser();
    Manifest2004Parser.Parsed parsed = parser.parse(manifest, sp.getId());

    // 5. 데이터베이스에 저장 (Resources → Orgs → Items)
    // 6. 응답 반환
}
```

### 4. 보안 기능

#### ZipSlip 공격 방지
```java
private static Path resolveZipEntry(Path destDir, ZipEntry entry) throws IOException {
    Path target = destDir.resolve(entry.getName()).normalize();
    if (!target.startsWith(destDir)) {
        throw new IOException("ZipSlip attack detected: " + entry.getName());
    }
    return target;
}
```

- 디렉토리 탐색 공격 차단
- 안전한 파일 추출 보장

### 5. XML 파싱

#### imsmanifest.xml 파싱 특징
- **DOM 파서** 사용
- **네임스페이스 인식** 지원
- **조직(organizations)** 구조 파싱
- **아이템(items)** 계층 구조 파싱
- **리소스(resources)** 정보 추출
- **시퀀싱 규칙**을 JSON으로 변환 저장

```java
// 시퀀싱 정보 JSON 변환 예시
Element seq = firstChild(itemEl, "sequencing");
if (seq != null) {
    Map<String, Object> seqJson = new LinkedHashMap<>();
    Element ctrl = firstChildNS(seq, "*", "controlMode");
    if (ctrl != null) {
        seqJson.put("flow", ctrl.getAttribute("flow"));
        seqJson.put("choice", ctrl.getAttribute("choice"));
        seqJson.put("forwardOnly", ctrl.getAttribute("forwardOnly"));
    }
    ObjectMapper om = new ObjectMapper();
    it.setSequencingJson(om.writeValueAsString(seqJson));
}
```

## 📊 API 응답 예시

```json
{
  "packageId": 1,
  "title": "SCORM Course",
  "orgCount": 1,
  "itemCount": 5,
  "resourceCount": 3,
  "contentRoot": "/tmp/scorm2004-xxxx"
}
```

## 🔧 설정

### application.yml
```yaml
spring:
  datasource:
    url: jdbc:h2:mem:scorm;MODE=MySQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  h2:
    console:
      enabled: true
      path: /h2
server:
  port: 8080
```

### 주요 설정 특징
- **H2 인메모리 데이터베이스** 사용
- **MySQL 호환 모드**
- **JPA 자동 스키마 생성**
- **H2 콘솔** 활성화 (`/h2`)

## 🚀 실행 방법

```bash
# Maven Wrapper 사용
./mvnw spring-boot:run

# 또는 Windows
mvnw.cmd spring-boot:run
```

## 💡 확장 가능한 부분

README에서 언급한 대로, 이는 **스켈레톤 구현**이며 다음과 같은 확장이 가능합니다:

### 1. 데이터 모델 확장
- **부모-자식 아이템 관계** 완전 구현
- **identifierref → resourceId 매핑** 개선
- **학습자 진도 추적** 테이블 추가

### 2. 기능 확장
- **완전한 시퀀싱 규칙** 구현
- **SCORM 런타임 API** 추가
- **학습 세션 관리** 기능
- **진도율 계산** 로직

### 3. 보안 및 성능
- **파일 크기 제한** 설정
- **비동기 처리** 도입
- **캐싱** 메커니즘 추가
- **로깅 및 모니터링** 강화

### 4. API 확장
- **패키지 목록 조회** API
- **패키지 삭제** API
- **메타데이터 조회** API
- **콘텐츠 서빙** API

## 🔍 코드 품질 특징

### 장점
- ✅ **명확한 패키지 구조**
- ✅ **보안 고려사항** (ZipSlip 방지)
- ✅ **Spring Boot 모범 사례** 준수
- ✅ **JPA 엔티티 설계** 적절
- ✅ **예외 처리** 포함

### 개선 가능한 부분
- ⚠️ **단위 테스트** 부재
- ⚠️ **에러 핸들링** 세분화 필요
- ⚠️ **로깅** 체계 개선 필요
- ⚠️ **설정 외부화** 고려
- ⚠️ **API 문서화** (Swagger 등) 추가

## 📝 결론

이 코드베이스는 SCORM 2004 패키지의 **기본적인 임포트와 메타데이터 추출**에 중점을 둔 **최소 기능 구현(MVP)**입니다. 

핵심 기능은 잘 구현되어 있으며, 보안 측면도 고려되어 있습니다. 향후 SCORM 런타임 기능이나 학습자 추적 기능을 추가하여 완전한 LMS 시스템으로 확장할 수 있는 좋은 기반을 제공합니다.
