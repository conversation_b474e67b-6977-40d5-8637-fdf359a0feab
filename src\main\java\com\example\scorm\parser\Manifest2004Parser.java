
package com.example.scorm.parser;

import com.example.scorm.model.*;
import org.w3c.dom.*;
import javax.xml.parsers.*;
import java.io.*;
import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;

public class Manifest2004Parser {

    public static class Parsed {
        public String title;
        public List<ScormOrg> orgs = new ArrayList<>();
        public List<ScormItem> items = new ArrayList<>();
        public List<ScormResource> resources = new ArrayList<>();
    }

    public Parsed parse(InputStream manifestXml, Long pkgId) throws Exception {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);
        DocumentBuilder db = dbf.newDocumentBuilder();
        Document doc = db.parse(manifestXml);
        doc.getDocumentElement().normalize();

        Parsed out = new Parsed();
        NodeList orgs = doc.getElementsByTagNameNS("*", "organization");
        Map<String, Long> orgIdMap = new HashMap<>();

        for (int i = 0; i < orgs.getLength(); i++) {
            Element org = (Element) orgs.item(i);
            ScormOrg o = new ScormOrg();
            o.setPackageId(pkgId);
            o.setIdentifier(org.getAttribute("identifier"));
            o.setTitle(textOf(org, "title"));
            if (i == 0) o.setDefaultOrg(true);
            out.orgs.add(o);
        }

        // resources
        NodeList resNodes = doc.getElementsByTagNameNS("*", "resource");
        Map<String, ScormResource> resById = new HashMap<>();
        for (int i = 0; i < resNodes.getLength(); i++) {
            Element r = (Element) resNodes.item(i);
            ScormResource sr = new ScormResource();
            sr.setPackageId(pkgId);
            sr.setIdentifier(r.getAttribute("identifier"));
            sr.setHref(r.getAttribute("href"));
            sr.setScormType(r.getAttributeNS("http://www.adlnet.org/xsd/adlcp_v1p3", "scormType"));
            resById.put(sr.getIdentifier(), sr);
            out.resources.add(sr);
        }

        // items (recursive)
        for (int i = 0; i < orgs.getLength(); i++) {
            Element org = (Element) orgs.item(i);
            String orgIdentifier = org.getAttribute("identifier");
            Long orgId = (long) (i + 1); // placeholder mapping; replaced after persisting
            orgIdMap.put(orgIdentifier, orgId);

            NodeList children = org.getChildNodes();
            for (int c = 0; c < children.getLength(); c++) {
                if (children.item(c) instanceof Element el && el.getLocalName() != null && el.getLocalName().equals("item")) {
                    traverseItem(pkgId, orgId, null, el, resById, out.items);
                }
            }
        }

        // package/course title (fallback to first org title)
        out.title = (out.orgs.size() > 0 ? Optional.ofNullable(out.orgs.get(0).getTitle()).orElse("SCORM Course") : "SCORM Course");
        return out;
    }

    private void traverseItem(Long pkgId, Long orgId, Long parentId, Element itemEl,
                              Map<String, ScormResource> resById, List<ScormItem> sink) throws Exception {
        ScormItem it = new ScormItem();
        it.setPackageId(pkgId);
        it.setOrgId(orgId);
        it.setParentId(parentId);
        it.setIdentifier(itemEl.getAttribute("identifier"));
        it.setTitle(textOf(itemEl, "title"));

        String resId = itemEl.getAttribute("identifierref");
        if (resId != null && !resId.isEmpty() && resById.containsKey(resId)) {
            // resourceId will be set by service after persistence mapping
        }

        // sequencing block -> keep as JSON string (minimal)
        Element seq = firstChild(itemEl, "sequencing");
        if (seq != null) {
            Map<String, Object> seqJson = new LinkedHashMap<>();
            Element ctrl = firstChildNS(seq, "*", "controlMode");
            if (ctrl != null) {
                seqJson.put("flow", ctrl.getAttribute("flow"));
                seqJson.put("choice", ctrl.getAttribute("choice"));
                seqJson.put("forwardOnly", ctrl.getAttribute("forwardOnly"));
            }
            ObjectMapper om = new ObjectMapper();
            it.setSequencingJson(om.writeValueAsString(seqJson));
        }

        sink.add(it);

        NodeList children = itemEl.getChildNodes();
        for (int c = 0; c < children.getLength(); c++) {
            if (children.item(c) instanceof Element el && el.getLocalName() != null && el.getLocalName().equals("item")) {
                traverseItem(pkgId, orgId, null, el, resById, sink);
            }
        }
    }

    private static String textOf(Element parent, String localName) {
        NodeList list = parent.getElementsByTagNameNS("*", localName);
        if (list.getLength() > 0) {
            return list.item(0).getTextContent();
        }
        return null;
    }

    private static Element firstChild(Element parent, String localName) {
        NodeList list = parent.getElementsByTagNameNS("*", localName);
        if (list.getLength() > 0) return (Element) list.item(0);
        return null;
    }

    private static Element firstChildNS(Element parent, String ns, String local) {
        NodeList list = parent.getElementsByTagNameNS("*", local);
        if (list.getLength() > 0) return (Element) list.item(0);
        return null;
    }
}
