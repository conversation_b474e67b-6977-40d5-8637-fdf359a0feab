# SCORM 2004 Importer 프로젝트 구동 가이드

## 📋 구동 확인 결과

### ✅ **성공 사항**
- **Spring Boot 애플리케이션 정상 시작** (2.478초)
- **Tomcat 서버 정상 구동** (포트 8080)
- **MyBatis 설정 정상 로드**
- **애플리케이션 컨텍스트 초기화 완료**

### ❌ **발생한 문제**
- **MySQL 서버 연결 실패**: `java.net.UnknownHostException: 알려진 호스트가 없습니다 (l43.201.31.215)`
- **데이터베이스 연결 불가**로 인한 API 요청 실패

## 🔧 **해결 방안**

### 1. **네트워크 연결 확인**

#### 호스트 연결 테스트
```bash
# Windows에서 호스트 연결 확인
ping l43.201.31.215

# 포트 연결 확인 (텔넷)
telnet l43.201.31.215 3306

# PowerShell에서 포트 확인
Test-NetConnection -ComputerName l43.201.31.215 -Port 3306
```

#### DNS 설정 확인
```bash
# DNS 조회 확인
nslookup l43.201.31.215
```

### 2. **MySQL 서버 상태 확인**

#### 서버 접근성 체크리스트
- [ ] MySQL 서버가 실행 중인지 확인
- [ ] 방화벽에서 3306 포트가 열려있는지 확인
- [ ] 원격 접속이 허용되어 있는지 확인
- [ ] 사용자 계정(`nidsuser`)이 원격 접속 권한을 가지고 있는지 확인

#### MySQL 원격 접속 설정 확인
```sql
-- MySQL 서버에서 실행
SELECT host, user FROM mysql.user WHERE user = 'nidsuser';
SHOW GRANTS FOR 'nidsuser'@'%';
```

### 3. **대안 설정 방법**

#### 3-1. 로컬 MySQL 사용
```yaml
# application.yml
spring:
  datasource:
    url: ****************************************************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

#### 3-2. H2 데이터베이스로 임시 전환
```yaml
# application.yml (개발/테스트용)
spring:
  datasource:
    url: jdbc:h2:mem:scorm;MODE=MySQL;DB_CLOSE_DELAY=-1
    username: sa
    password:
    driver-class-name: org.h2.Driver
  h2:
    console:
      enabled: true
      path: /h2
```

#### 3-3. Docker MySQL 컨테이너 사용
```bash
# Docker로 MySQL 실행
docker run --name mysql-scorm \
  -e MYSQL_ROOT_PASSWORD=password \
  -e MYSQL_DATABASE=scorm2004 \
  -e MYSQL_USER=scormuser \
  -e MYSQL_PASSWORD=scormpass \
  -p 3306:3306 \
  -d mysql:8.0
```

### 4. **데이터베이스 스키마 생성**

#### MySQL 스키마 실행
```sql
-- schema.sql 내용 실행
CREATE DATABASE IF NOT EXISTS scorm2004 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE scorm2004;

-- 테이블 생성 스크립트 실행
-- (src/main/resources/schema.sql 참조)
```

## 🚀 **권장 구동 절차**

### 1단계: 환경 준비
```bash
# 1. MySQL 서버 연결 확인
ping l43.201.31.215

# 2. 포트 접근 확인
telnet l43.201.31.215 3306
```

### 2단계: 데이터베이스 설정
```sql
-- MySQL 서버에 접속하여 실행
CREATE DATABASE IF NOT EXISTS test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 사용자 권한 확인/설정
GRANT ALL PRIVILEGES ON test.* TO 'nidsuser'@'%';
FLUSH PRIVILEGES;
```

### 3단계: 스키마 생성
```bash
# schema.sql 파일을 MySQL 서버에 실행
mysql -h l43.201.31.215 -u nidsuser -p test < src/main/resources/schema.sql
```

### 4단계: 애플리케이션 실행
```bash
# Maven으로 실행
mvn spring-boot:run

# 또는 JAR 파일로 실행
mvn clean package
java -jar target/scorm2004-importer-0.0.1-SNAPSHOT.jar
```

## 🔍 **문제 해결 체크리스트**

### 네트워크 문제
- [ ] 호스트명 해석 가능 여부 확인
- [ ] 네트워크 연결 상태 확인
- [ ] 방화벽 설정 확인
- [ ] VPN 연결 필요 여부 확인

### MySQL 서버 문제
- [ ] MySQL 서비스 실행 상태
- [ ] 포트 3306 바인딩 상태
- [ ] 원격 접속 허용 설정
- [ ] 사용자 계정 권한 설정

### 애플리케이션 설정 문제
- [ ] JDBC URL 형식 확인
- [ ] 사용자명/비밀번호 정확성
- [ ] 데이터베이스명 존재 여부
- [ ] 타임존 설정 적절성

## 📊 **현재 구성 상태**

### 기술 스택
- ✅ **Java 17** + **Spring Boot 3.3.2**
- ✅ **MyBatis 3.0.3** (정상 로드됨)
- ❌ **MySQL 8.0.33** (연결 실패)
- ✅ **Maven** (빌드 성공)

### 애플리케이션 상태
- ✅ **Spring 컨텍스트 초기화** 완료
- ✅ **Tomcat 서버** 정상 구동 (포트 8080)
- ✅ **MyBatis 매퍼** 스캔 완료
- ❌ **데이터베이스 연결** 실패

### API 엔드포인트
- **POST** `/api/scorm2004/packages` - SCORM 패키지 업로드
- **상태**: 데이터베이스 연결 문제로 현재 사용 불가

## 💡 **즉시 해결 방안**

### 임시 해결책 (H2 사용)
1. `pom.xml`에 H2 의존성 추가
2. `application.yml`에서 H2 설정으로 변경
3. 애플리케이션 재시작

### 영구 해결책 (MySQL 연결 수정)
1. 네트워크 관리자와 MySQL 서버 접근성 확인
2. 정확한 호스트명/IP 주소 확인
3. 데이터베이스 서버 설정 점검
4. 연결 정보 수정 후 재배포

## 📞 **지원 연락처**

MySQL 서버 연결 문제 해결을 위해 다음 정보가 필요합니다:
- MySQL 서버 관리자 연락처
- 정확한 서버 주소 및 포트
- 데이터베이스 접근 권한 확인
- 네트워크 방화벽 설정 정보
