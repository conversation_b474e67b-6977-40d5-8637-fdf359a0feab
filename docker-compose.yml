version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: scorm-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: scorm2004
      MYSQL_USER: scormuser
      MYSQL_PASSWORD: scormpass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/main/resources/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - scorm-network

  app:
    build: .
    container_name: scorm-app
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    environment:
      SPRING_DATASOURCE_URL: *****************************************************************************************************
      SPRING_DATASOURCE_USERNAME: scormuser
      SPRING_DATASOURCE_PASSWORD: scormpass
    networks:
      - scorm-network

volumes:
  mysql_data:

networks:
  scorm-network:
    driver: bridge
