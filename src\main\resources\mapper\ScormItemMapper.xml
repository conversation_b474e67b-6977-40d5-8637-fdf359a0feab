<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.scorm.mapper.ScormItemMapper">

    <resultMap id="ScormItemResultMap" type="com.example.scorm.model.ScormItem">
        <id property="id" column="id"/>
        <result property="packageId" column="package_id"/>
        <result property="orgId" column="org_id"/>
        <result property="identifier" column="identifier"/>
        <result property="parentId" column="parent_id"/>
        <result property="resourceId" column="resource_id"/>
        <result property="title" column="title"/>
        <result property="sequencingJson" column="sequencing_json"/>
        <result property="sco" column="is_sco"/>
    </resultMap>

    <insert id="insert" parameterType="com.example.scorm.model.ScormItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO scorm2004_item (package_id, org_id, identifier, parent_id, resource_id, title, sequencing_json, is_sco)
        VALUES (#{packageId}, #{orgId}, #{identifier}, #{parentId}, #{resourceId}, #{title}, #{sequencingJson}, #{sco})
    </insert>

    <update id="update" parameterType="com.example.scorm.model.ScormItem">
        UPDATE scorm2004_item 
        SET package_id = #{packageId}, 
            org_id = #{orgId}, 
            identifier = #{identifier}, 
            parent_id = #{parentId}, 
            resource_id = #{resourceId}, 
            title = #{title}, 
            sequencing_json = #{sequencingJson}, 
            is_sco = #{sco}
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="Long" resultMap="ScormItemResultMap">
        SELECT id, package_id, org_id, identifier, parent_id, resource_id, title, sequencing_json, is_sco
        FROM scorm2004_item
        WHERE id = #{id}
    </select>

    <delete id="deleteById" parameterType="Long">
        DELETE FROM scorm2004_item WHERE id = #{id}
    </delete>

</mapper>
