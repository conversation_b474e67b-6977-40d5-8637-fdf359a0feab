
package com.example.scorm.model;

import jakarta.persistence.*;

@Entity
@Table(name = "scorm2004_resource")
public class ScormResource {
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long packageId;
    private String identifier;
    @Column(length = 1000)
    private String href;
    private String scormType; // sco or asset
    public Long getId() { return id; }
    public Long getPackageId() { return packageId; }
    public void setPackageId(Long packageId) { this.packageId = packageId; }
    public String getIdentifier() { return identifier; }
    public void setIdentifier(String identifier) { this.identifier = identifier; }
    public String getHref() { return href; }
    public void setHref(String href) { this.href = href; }
    public String getScormType() { return scormType; }
    public void setScormType(String scormType) { this.scormType = scormType; }
}
