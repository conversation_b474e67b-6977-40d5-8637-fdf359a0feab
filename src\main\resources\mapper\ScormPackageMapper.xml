<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.scorm.mapper.ScormPackageMapper">

    <resultMap id="ScormPackageResultMap" type="com.example.scorm.model.ScormPackage">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="contentRoot" column="content_root"/>
    </resultMap>

    <insert id="insert" parameterType="com.example.scorm.model.ScormPackage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO scorm2004_package (title, status, created_at, content_root)
        VALUES (#{title}, #{status}, #{createdAt}, #{contentRoot})
    </insert>

    <update id="update" parameterType="com.example.scorm.model.ScormPackage">
        UPDATE scorm2004_package 
        SET title = #{title}, 
            status = #{status}, 
            created_at = #{createdAt}, 
            content_root = #{contentRoot}
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="Long" resultMap="ScormPackageResultMap">
        SELECT id, title, status, created_at, content_root
        FROM scorm2004_package
        WHERE id = #{id}
    </select>

    <delete id="deleteById" parameterType="Long">
        DELETE FROM scorm2004_package WHERE id = #{id}
    </delete>

</mapper>
