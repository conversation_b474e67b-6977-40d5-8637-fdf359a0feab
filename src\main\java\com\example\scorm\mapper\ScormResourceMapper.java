package com.example.scorm.mapper;

import com.example.scorm.model.ScormResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ScormResourceMapper {
    
    void insert(ScormResource scormResource);
    
    void update(ScormResource scormResource);
    
    ScormResource findById(@Param("id") Long id);
    
    void deleteById(@Param("id") Long id);
}
