<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.scorm.mapper.ScormResourceMapper">

    <resultMap id="ScormResourceResultMap" type="com.example.scorm.model.ScormResource">
        <id property="id" column="id"/>
        <result property="packageId" column="package_id"/>
        <result property="identifier" column="identifier"/>
        <result property="href" column="href"/>
        <result property="scormType" column="scorm_type"/>
    </resultMap>

    <insert id="insert" parameterType="com.example.scorm.model.ScormResource" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO scorm2004_resource (package_id, identifier, href, scorm_type)
        VALUES (#{packageId}, #{identifier}, #{href}, #{scormType})
    </insert>

    <update id="update" parameterType="com.example.scorm.model.ScormResource">
        UPDATE scorm2004_resource 
        SET package_id = #{packageId}, 
            identifier = #{identifier}, 
            href = #{href}, 
            scorm_type = #{scormType}
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="Long" resultMap="ScormResourceResultMap">
        SELECT id, package_id, identifier, href, scorm_type
        FROM scorm2004_resource
        WHERE id = #{id}
    </select>

    <delete id="deleteById" parameterType="Long">
        DELETE FROM scorm2004_resource WHERE id = #{id}
    </delete>

</mapper>
