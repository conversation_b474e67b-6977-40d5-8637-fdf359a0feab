
package com.example.scorm.model;

import jakarta.persistence.*;
import java.time.Instant;

@Entity
@Table(name = "scorm2004_package")
public class ScormPackage {
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String title;
    private String status;
    private Instant createdAt = Instant.now();
    private String contentRoot; // extracted path
    public Long getId() { return id; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public Instant getCreatedAt() { return createdAt; }
    public String getContentRoot() { return contentRoot; }
    public void setContentRoot(String contentRoot) { this.contentRoot = contentRoot; }
}
