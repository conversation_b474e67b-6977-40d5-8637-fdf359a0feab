
package com.example.scorm.util;

import java.io.*;
import java.nio.file.*;
import java.util.zip.*;

public class SafeZip {
    public static Path extractZip(InputStream in, Path destDir) throws IOException {
        if (!Files.exists(destDir)) Files.createDirectories(destDir);
        try (ZipInputStream zis = new ZipInputStream(new BufferedInputStream(in))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                Path newPath = resolveZipEntry(destDir, entry);
                if (entry.isDirectory()) {
                    Files.createDirectories(newPath);
                } else {
                    if (newPath.getParent() != null) Files.createDirectories(newPath.getParent());
                    try (OutputStream os = Files.newOutputStream(newPath)) {
                        zis.transferTo(os);
                    }
                }
            }
        }
        return destDir;
    }

    private static Path resolveZipEntry(Path destDir, ZipEntry entry) throws IOException {
        Path target = destDir.resolve(entry.getName()).normalize();
        if (!target.startsWith(destDir)) {
            throw new IOException("ZipSlip attack detected: " + entry.getName());
        }
        return target;
    }
}
