<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.scorm.mapper.ScormOrgMapper">

    <resultMap id="ScormOrgResultMap" type="com.example.scorm.model.ScormOrg">
        <id property="id" column="id"/>
        <result property="packageId" column="package_id"/>
        <result property="identifier" column="identifier"/>
        <result property="title" column="title"/>
        <result property="defaultOrg" column="is_default_org"/>
    </resultMap>

    <insert id="insert" parameterType="com.example.scorm.model.ScormOrg" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO scorm2004_org (package_id, identifier, title, is_default_org)
        VALUES (#{packageId}, #{identifier}, #{title}, #{defaultOrg})
    </insert>

    <update id="update" parameterType="com.example.scorm.model.ScormOrg">
        UPDATE scorm2004_org 
        SET package_id = #{packageId}, 
            identifier = #{identifier}, 
            title = #{title}, 
            is_default_org = #{defaultOrg}
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="Long" resultMap="ScormOrgResultMap">
        SELECT id, package_id, identifier, title, is_default_org
        FROM scorm2004_org
        WHERE id = #{id}
    </select>

    <delete id="deleteById" parameterType="Long">
        DELETE FROM scorm2004_org WHERE id = #{id}
    </delete>

</mapper>
