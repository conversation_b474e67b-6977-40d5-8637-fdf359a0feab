
package com.example.scorm.service;

import com.example.scorm.model.*;
import com.example.scorm.parser.Manifest2004Parser;
import com.example.scorm.repo.*;
import com.example.scorm.util.SafeZip;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.*;
import java.util.*;

@Service
public class Scorm2004ImportService {

    private final ScormPackageRepo pkgRepo;
    private final ScormOrgRepo orgRepo;
    private final ScormItemRepo itemRepo;
    private final ScormResourceRepo resRepo;

    public Scorm2004ImportService(ScormPackageRepo pkgRepo, ScormOrgRepo orgRepo, ScormItemRepo itemRepo, ScormResourceRepo resRepo) {
        this.pkgRepo = pkgRepo;
        this.orgRepo = orgRepo;
        this.itemRepo = itemRepo;
        this.resRepo = resRepo;
    }

    public Map<String, Object> importZip(MultipartFile zip) throws Exception {
        // 1) Extract safely
        Path dest = Files.createTempDirectory("scorm2004-");
        SafeZip.extractZip(zip.getInputStream(), dest);

        // 2) Locate imsmanifest.xml
        Path manifest = Files.walk(dest)
                .filter(p -> p.getFileName().toString().equalsIgnoreCase("imsmanifest.xml"))
                .findFirst().orElseThrow(() -> new FileNotFoundException("imsmanifest.xml not found"));

        // 3) Create package row (title will be updated after parse)
        ScormPackage sp = new ScormPackage();
        sp.setTitle("SCORM Course");
        sp.setStatus("ACTIVE");
        sp.setContentRoot(dest.toAbsolutePath().toString());
        sp = pkgRepo.save(sp);

        // 4) Parse
        Manifest2004Parser parser = new Manifest2004Parser();
        Manifest2004Parser.Parsed parsed;
        try (InputStream in = Files.newInputStream(manifest)) {
            parsed = parser.parse(in, sp.getId());
        }

        // 5) Persist resources
        Map<String, Long> resIdMap = new HashMap<>();
        for (ScormResource sr : parsed.resources) {
            sr.setPackageId(sp.getId());
            ScormResource saved = resRepo.save(sr);
            resIdMap.put(saved.getIdentifier(), saved.getId());
        }

        // 6) Persist orgs
        Map<String, Long> orgIdMap = new HashMap<>();
        for (ScormOrg so : parsed.orgs) {
            so.setPackageId(sp.getId());
            ScormOrg saved = orgRepo.save(so);
            orgIdMap.put(so.getIdentifier(), saved.getId());
        }

        // 7) Persist items (simple flat save; parent linking omitted for brevity)
        for (ScormItem si : parsed.items) {
            si.setPackageId(sp.getId());
            if (si.getOrgId() != null) {
                // remap temp org id to real if needed (here we just set first org id)
                if (!orgIdMap.isEmpty()) {
                    si.setOrgId(orgIdMap.values().iterator().next());
                }
            }
            // resolve resourceId by identifierref if present inside sequencingJson? (parser omitted it)
            // In a production parser, carry identifierref; here we skip or set later.
            itemRepo.save(si);
        }

        // 8) Update package title
        sp.setTitle(parsed.title);
        pkgRepo.save(sp);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("packageId", sp.getId());
        result.put("title", sp.getTitle());
        result.put("orgCount", parsed.orgs.size());
        result.put("itemCount", parsed.items.size());
        result.put("resourceCount", parsed.resources.size());
        result.put("contentRoot", sp.getContentRoot());
        return result;
    }
}
