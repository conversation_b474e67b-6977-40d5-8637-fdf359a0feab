package com.example.scorm.mapper;

import com.example.scorm.model.ScormPackage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ScormPackageMapper {
    
    void insert(ScormPackage scormPackage);
    
    void update(ScormPackage scormPackage);
    
    ScormPackage findById(@Param("id") Long id);
    
    void deleteById(@Param("id") Long id);
}
