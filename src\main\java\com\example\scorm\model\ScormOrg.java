
package com.example.scorm.model;

import jakarta.persistence.*;

@Entity
@Table(name = "scorm2004_org")
public class ScormOrg {
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long packageId;
    private String identifier;
    private String title;
    private boolean isDefaultOrg;
    public Long getId() { return id; }
    public Long getPackageId() { return packageId; }
    public void setPackageId(Long packageId) { this.packageId = packageId; }
    public String getIdentifier() { return identifier; }
    public void setIdentifier(String identifier) { this.identifier = identifier; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public boolean isDefaultOrg() { return isDefaultOrg; }
    public void setDefaultOrg(boolean defaultOrg) { isDefaultOrg = defaultOrg; }
}
