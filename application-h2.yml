# H2 데이터베이스 사용 설정 (테스트/개발용)
# 사용법: mvn spring-boot:run -Dspring-boot.run.profiles=h2

spring:
  datasource:
    url: jdbc:h2:mem:scorm;MODE=MySQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver
  
  h2:
    console:
      enabled: true
      path: /h2
      settings:
        web-allow-others: true

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.scorm.model
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

server:
  port: 8080

logging:
  level:
    com.example.scorm.mapper: DEBUG
    org.springframework.jdbc: DEBUG

# H2 데이터베이스 초기화 스크립트
sql:
  init:
    mode: always
    schema-locations: classpath:h2-schema.sql
