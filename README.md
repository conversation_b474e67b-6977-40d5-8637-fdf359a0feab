
# SCORM 2004 Importer (Spring Boot)

Minimal Java Spring Boot service to **register/import SCORM 2004 packages**.

## Run

```bash
./mvnw spring-boot:run
```

## API

- `POST /api/scorm2004/packages` (multipart form)
  - field name: `file` (zip)

**Response example**

```json
{
  "packageId": 1,
  "title": "SCORM Course",
  "orgCount": 1,
  "itemCount": 5,
  "resourceCount": 3,
  "contentRoot": "/tmp/scorm2004-xxxx"
}
```

## Notes

- Safe ZIP extraction (ZipSlip guarded)
- Minimal `imsmanifest.xml` DOM parser (orgs/items/resources + controlMode to JSON)
- H2 in-memory DB + JPA entities
- This is a skeleton: you can extend parent-child item relations, identifierref → resourceId mapping, and full sequencing.
