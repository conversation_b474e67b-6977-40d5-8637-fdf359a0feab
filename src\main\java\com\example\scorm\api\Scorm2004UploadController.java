
package com.example.scorm.api;

import com.example.scorm.service.Scorm2004ImportService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.Map;

@RestController
@RequestMapping("/api/scorm2004")
public class Scorm2004UploadController {

    private final Scorm2004ImportService importer;

    public Scorm2004UploadController(Scorm2004ImportService importer) {
        this.importer = importer;
    }

    @PostMapping(value="/packages", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Map<String,Object> upload(@RequestPart("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) throw new IllegalArgumentException("Empty file");
        if (!file.getOriginalFilename().toLowerCase().endsWith(".zip")) {
            throw new IllegalArgumentException("Only .zip is allowed");
        }
        return importer.importZip(file);
    }
}
